<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Informe OSINT - alineasolar.com</title>
    <style>
        @page {
            margin: 2cm;
            size: A4;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .container {
            background: white;
            margin: 20px auto;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 900px;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 30px;
            margin-bottom: 40px;
        }
        
        .logo {
            width: 120px;
            height: auto;
            margin-bottom: 20px;
        }
        
        .company-name {
            color: #667eea;
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        h1 {
            color: #2c3e50;
            font-size: 32px;
            margin: 20px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        h2 {
            color: #667eea;
            font-size: 24px;
            border-left: 4px solid #667eea;
            padding-left: 15px;
            margin-top: 30px;
        }
        
        h3 {
            color: #34495e;
            font-size: 18px;
            margin-top: 25px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .info-card {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .info-card h4 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 16px;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .critical {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        
        tr:hover {
            background-color: #f5f5f5;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #667eea;
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .date {
            color: #7f8c8d;
            font-size: 14px;
            text-align: right;
            margin-bottom: 20px;
        }
        
        .executive-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin: 30px 0;
        }
        
        .risk-level {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 12px;
        }
        
        .risk-low { background: #27ae60; color: white; }
        .risk-medium { background: #f39c12; color: white; }
        .risk-high { background: #e74c3c; color: white; }
        
        @media print {
            body { background: white; }
            .container { margin: 0; box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" alt="KareDesk Logo" class="logo">
            <div class="company-name">KAREDESK</div>
            <h1>INFORME OSINT EJECUTIVO</h1>
            <h2 style="border: none; padding: 0; color: #2c3e50;">alineasolar.com</h2>
            <div class="date">Fecha: 26 de Junio, 2025</div>
        </div>

        <div class="executive-summary">
            <h2 style="color: white; border: none; padding: 0;">RESUMEN EJECUTIVO</h2>
            <p>Este informe presenta un análisis completo de reconocimiento OSINT (Open Source Intelligence) del dominio <strong>alineasolar.com</strong>. El análisis revela información crítica sobre la infraestructura, configuración de seguridad y exposición digital de la organización.</p>
            
            <div style="margin-top: 20px;">
                <strong>Nivel de Riesgo General:</strong> <span class="risk-level risk-medium">MEDIO</span>
            </div>
        </div>

        <h2>🔍 INFORMACIÓN DEL DOMINIO</h2>
        <div class="info-grid">
            <div class="info-card">
                <h4>Datos Básicos del Dominio</h4>
                <strong>Dominio:</strong> alineasolar.com<br>
                <strong>IP Principal:</strong> *************<br>
                <strong>Registrador:</strong> eNom, LLC<br>
                <strong>Fecha de Creación:</strong> 2 de Mayo, 2006<br>
                <strong>Fecha de Expiración:</strong> 2 de Mayo, 2026
            </div>
            
            <div class="info-card">
                <h4>Servidores DNS</h4>
                <strong>NS1:</strong> dns255248.phdns19.es<br>
                <strong>NS2:</strong> dns255249.phdns19.es<br>
                <strong>Proveedor:</strong> Profesional Hosting
            </div>
        </div>

        <h2>📧 CONFIGURACIÓN DE CORREO</h2>
        <div class="info-card">
            <h4>Registros MX y SPF</h4>
            <strong>Servidor de Correo:</strong> mail.alineasolar.com (*************)<br>
            <strong>Prioridad MX:</strong> 10<br>
            <strong>Registro SPF:</strong> v=spf1 ip4:************* include:spf.profesionalhosting.com -all
        </div>

        <div class="success">
            <strong>✅ Configuración SPF Correcta:</strong> El dominio tiene configurado correctamente el registro SPF, lo que ayuda a prevenir el spoofing de correo electrónico.
        </div>

        <h2>🌐 ANÁLISIS DE SUBDOMINIOS</h2>
        <table>
            <thead>
                <tr>
                    <th>Subdominio</th>
                    <th>IP</th>
                    <th>Estado</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>www.alineasolar.com</td>
                    <td>*************</td>
                    <td>Activo</td>
                </tr>
                <tr>
                    <td>mail.alineasolar.com</td>
                    <td>*************</td>
                    <td>Activo</td>
                </tr>
            </tbody>
        </table>

        <h2>🔒 ANÁLISIS DE PUERTOS Y SERVICIOS</h2>
        <table>
            <thead>
                <tr>
                    <th>Puerto</th>
                    <th>Servicio</th>
                    <th>Estado</th>
                    <th>Nivel de Riesgo</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>21</td>
                    <td>FTP</td>
                    <td>Abierto</td>
                    <td><span class="risk-level risk-high">ALTO</span></td>
                </tr>
                <tr>
                    <td>22</td>
                    <td>SSH</td>
                    <td>Abierto</td>
                    <td><span class="risk-level risk-medium">MEDIO</span></td>
                </tr>
                <tr>
                    <td>25</td>
                    <td>SMTP</td>
                    <td>Abierto</td>
                    <td><span class="risk-level risk-medium">MEDIO</span></td>
                </tr>
                <tr>
                    <td>53</td>
                    <td>DNS</td>
                    <td>Abierto</td>
                    <td><span class="risk-level risk-low">BAJO</span></td>
                </tr>
                <tr>
                    <td>80</td>
                    <td>HTTP</td>
                    <td>Abierto</td>
                    <td><span class="risk-level risk-low">BAJO</span></td>
                </tr>
                <tr>
                    <td>110</td>
                    <td>POP3</td>
                    <td>Abierto</td>
                    <td><span class="risk-level risk-medium">MEDIO</span></td>
                </tr>
                <tr>
                    <td>143</td>
                    <td>IMAP</td>
                    <td>Abierto</td>
                    <td><span class="risk-level risk-medium">MEDIO</span></td>
                </tr>
                <tr>
                    <td>443</td>
                    <td>HTTPS</td>
                    <td>Abierto</td>
                    <td><span class="risk-level risk-low">BAJO</span></td>
                </tr>
                <tr>
                    <td>993</td>
                    <td>IMAPS</td>
                    <td>Abierto</td>
                    <td><span class="risk-level risk-low">BAJO</span></td>
                </tr>
                <tr>
                    <td>995</td>
                    <td>POP3S</td>
                    <td>Abierto</td>
                    <td><span class="risk-level risk-low">BAJO</span></td>
                </tr>
            </tbody>
        </table>

        <h2>🖥️ TECNOLOGÍAS IDENTIFICADAS</h2>
        <div class="info-grid">
            <div class="info-card">
                <h4>Servidor Web</h4>
                <strong>Servidor:</strong> LiteSpeed<br>
                <strong>PHP:</strong> 7.4.33<br>
                <strong>CMS:</strong> WordPress<br>
                <strong>Hosting:</strong> Hostinger
            </div>
            
            <div class="info-card">
                <h4>Características de Seguridad</h4>
                <strong>HTTPS:</strong> ✅ Habilitado<br>
                <strong>HTTP/2:</strong> ✅ Soportado<br>
                <strong>CSP:</strong> ✅ upgrade-insecure-requests<br>
                <strong>Cache:</strong> LiteSpeed Cache activo
            </div>
        </div>

        <h2>⚠️ HALLAZGOS CRÍTICOS</h2>
        
        <div class="critical">
            <strong>🚨 RIESGO ALTO - Puerto FTP Abierto:</strong><br>
            El puerto 21 (FTP) está abierto y accesible desde Internet. FTP transmite credenciales en texto plano y es vulnerable a ataques de fuerza bruta.
        </div>

        <div class="warning">
            <strong>⚠️ RIESGO MEDIO - Múltiples Servicios de Correo:</strong><br>
            Los puertos 25, 110, y 143 están abiertos, exponiendo servicios de correo que podrían ser objetivos de ataques.
        </div>

        <div class="warning">
            <strong>⚠️ RIESGO MEDIO - PHP Desactualizado:</strong><br>
            La versión PHP 7.4.33 está cerca del fin de soporte y podría contener vulnerabilidades conocidas.
        </div>

        <h2>📊 RECOMENDACIONES DE SEGURIDAD</h2>
        
        <h3>Prioridad Alta</h3>
        <ul>
            <li><strong>Cerrar puerto FTP (21):</strong> Migrar a SFTP/SCP para transferencias seguras</li>
            <li><strong>Implementar fail2ban:</strong> Para proteger SSH y otros servicios contra ataques de fuerza bruta</li>
            <li><strong>Actualizar PHP:</strong> Migrar a PHP 8.x para mejorar seguridad y rendimiento</li>
        </ul>

        <h3>Prioridad Media</h3>
        <ul>
            <li><strong>Configurar firewall:</strong> Restringir acceso a puertos administrativos</li>
            <li><strong>Implementar 2FA:</strong> Para accesos administrativos</li>
            <li><strong>Monitoreo de logs:</strong> Implementar sistema de monitoreo proactivo</li>
        </ul>

        <h3>Prioridad Baja</h3>
        <ul>
            <li><strong>Headers de seguridad:</strong> Implementar HSTS, X-Frame-Options, etc.</li>
            <li><strong>Certificado SSL:</strong> Verificar configuración y renovación automática</li>
            <li><strong>Backup automatizado:</strong> Verificar estrategia de respaldo</li>
        </ul>

        <div class="footer">
            <p><strong>KareDesk - Cybersecurity Intelligence</strong></p>
            <p>Este informe es confidencial y está destinado únicamente para uso interno de la organización.</p>
            <p>Generado automáticamente el 26 de Junio, 2025</p>
        </div>
    </div>
</body>
</html>
